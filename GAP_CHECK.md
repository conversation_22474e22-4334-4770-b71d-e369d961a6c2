# MentorMatchHub - Feature Gap Analysis & Next Development Sprint

## 1. Feature Gap Analysis

### ✅ Completed Features

- **User Authentication & Authorization**

  - Password-based authentication system
  - Role-based access control (admin, mentor, mentee)
  - Protected routes and session management

- **Organization Management**

  - ESO onboarding and profile setup
  - Organization branding customization (colors, logo, about)
  - Basic organization data storage

- **Form Builder & Public Forms**

  - Dynamic form creation for mentor/mentee onboarding
  - Public form submission without login requirement
  - Customizable form fields (text, textarea, select, multiselect, checkbox, radio, email, number)

- **Mentor & Mentee Management**

  - CRUD operations for mentors and mentees
  - Approval workflow for new applications
  - Email notifications for approved users
  - Profile management with custom form responses

- **AI-Powered Matching System**

  - Sophisticated matching algorithm based on expertise, industry, availability, and meeting preferences
  - Match scoring and reasoning generation
  - Bulk match generation capabilities

- **Match Management**

  - Match creation, approval, and status tracking
  - Session scheduling and feedback collection
  - Basic email infrastructure (Resend integration)
  - Welcome emails for approved mentors/mentees (functional)

- **Analytics Dashboard**

  - Basic metrics (total matches, active mentors, completed sessions, average ratings)
  - Organization-level analytics

- **Subscription Management**
  - Stripe integration for payment processing
  - Multiple subscription tiers (Basic, Professional, Enterprise)
  - Checkout session handling and subscription verification

### 🎯 Missing Features

- **Code Organization & Structure**

  - Modular file organization by feature/domain
  - Consistent naming conventions and folder structure
  - Separation of concerns between business logic and presentation
  - Clear dependency management and import patterns

- **Google OAuth Integration**

  - Google OAuth 2.0 authentication setup
  - Google Sign-In button integration
  - User profile data synchronization from Google
  - OAuth token management and refresh

- **File Upload Infrastructure**

  - Profile image upload for ESOs
  - Document/attachment support for forms
  - Secure file storage and serving

- **Email System Enhancement & Brevo Migration**

  - **Provider Migration**: Switch from Resend to Brevo for better deliverability and features
  - **Invitation Email Implementation**: Currently stubbed - needs actual email sending with form links
  - **Introduction Email Implementation**: Match approval emails are stubbed
  - **Follow-up Email Implementation**: Follow-up emails are stubbed
  - **Feedback Email Implementation**: Session feedback emails are stubbed
  - **Email Template Storage**: Templates exist in frontend but not persisted in database
  - **Custom Domain Email**: Currently using resend.dev domain instead of organization domains
  - **Email Settings Management**: From address, reply-to, and footer settings not functional

- **Spam Prevention**

  - Rate limiting for form submissions
  - CAPTCHA integration
  - Email verification for form submissions

- **Form Enhancement**

  - Link embedding in form questions (terms of service, etc.)
  - File upload fields in forms
  - Conditional form logic

- **Help System**

  - Help icon functionality and documentation
  - User guides and tutorials
  - Support ticket system

- **Domain & Deployment**

  - Custom domain configuration
  - Production deployment setup
  - SSL certificate management

- **Activity Tracking**
  - Comprehensive activity logging
  - User action history
  - System audit trails

---

## 2. User Story Generation

**Most Critical Feature: Code Organization & Structure**

### Primary User Story - Code Organization

As a **developer working on MentorMatchHub**, I want **the codebase to be organized by feature domains with clear separation of concerns** so that **I can quickly understand, maintain, and extend the application without getting lost in scattered files**.

#### Acceptance Criteria - Code Organization

1. **Feature-Based Structure**: Code is organized into feature modules (auth, organizations, mentors, mentees, matches, forms, etc.)
2. **Consistent Naming**: Files and folders follow consistent naming conventions (kebab-case, descriptive names)
3. **Separation of Concerns**: Business logic, data access, API routes, and UI components are clearly separated
4. **Shared Components**: Common utilities, types, and components are centralized in shared directories
5. **Clear Dependencies**: Import paths are logical and dependencies flow in one direction
6. **Documentation**: Each module has clear README files explaining its purpose and structure
7. **Type Safety**: TypeScript types are co-located with their related functionality
8. **Test Organization**: Tests mirror the source code structure for easy navigation
9. **Build Configuration**: Build and configuration files are organized and well-documented
10. **Developer Experience**: New developers can quickly understand the codebase structure and find relevant files

### Secondary User Story - Email System Enhancement & Brevo Migration

As an **ESO administrator**, I want **a complete email system that actually sends invitation, introduction, and follow-up emails using a professional email provider** so that **I can effectively communicate with mentors and mentees throughout their journey**.

#### Acceptance Criteria - Email System Enhancement

1. **Brevo Integration**: Replace Resend with Brevo for improved deliverability and features
2. **Invitation Emails**: Functional invitation emails with form links that actually get sent
3. **Introduction Emails**: Match approval triggers actual introduction emails to mentor and mentee
4. **Follow-up Emails**: Automated follow-up emails for unscheduled matches
5. **Feedback Emails**: Session completion triggers feedback request emails
6. **Template Persistence**: Email templates are stored in database and customizable per organization
7. **Custom Domains**: Support for organization-branded email addresses (e.g., <EMAIL>)
8. **Email Settings**: Functional from address, reply-to, and footer configuration
9. **Email Tracking**: Delivery status and open/click tracking for sent emails
10. **Error Handling**: Proper error handling and retry logic for failed email sends

### Tertiary User Story - Profile Upload Infrastructure

As an **ESO administrator**, I want **to upload and display my organization's logo and profile images** so that **I can create a professional, branded experience for my mentors and mentees**.

#### Acceptance Criteria - Profile Upload Infrastructure

1. **File Upload Interface**: ESO admins can upload image files (PNG, JPG, JPEG) up to 5MB in size through the branding page
2. **Image Processing**: Uploaded images are automatically resized and optimized for web display
3. **Secure Storage**: Images are stored securely with unique identifiers and proper access controls
4. **Display Integration**: Uploaded logos appear in the organization's branding across all public forms and admin interface
5. **Validation**: System validates file type, size, and dimensions before accepting uploads
6. **Error Handling**: Clear error messages are displayed for invalid files or upload failures
7. **Fallback Display**: Default placeholder logo is shown when no custom logo is uploaded
8. **Update Capability**: Admins can replace existing logos with new uploads
9. **Performance**: Image loading does not significantly impact page load times
10. **Mobile Compatibility**: Logo displays correctly on mobile devices and responsive layouts

### Quaternary User Story - Google OAuth Integration

As an **ESO administrator**, I want **to sign in using my Google account** so that **I can quickly and securely access the platform without managing another password**.

#### Acceptance Criteria - Google OAuth Integration

1. **Google OAuth Setup**: Google OAuth 2.0 is properly configured with valid client credentials
2. **Sign-In Button**: Users can see and click a "Sign in with Google" button on the login page
3. **Authentication Flow**: Clicking the Google sign-in button redirects users to Google's OAuth consent screen
4. **User Creation**: New users signing in with Google are automatically created in the system with appropriate default roles
5. **Profile Sync**: User's name, email, and profile picture are synchronized from their Google account
6. **Session Management**: Google OAuth sessions are properly managed with secure token storage
7. **Fallback Support**: Password-based authentication remains available as an alternative
8. **Error Handling**: Clear error messages are displayed for OAuth failures or permission denials
9. **Security**: OAuth tokens are securely stored and refreshed as needed
10. **Role Assignment**: New Google OAuth users are assigned appropriate default roles (admin for ESO setup)

---

## 4. Optional User Stories for Architectural Decisions

### Option A: Database Schema Relationships Enhancement (PostgreSQL + Drizzle)

As a **developer working on MentorMatchHub**, I want **proper database relationships and foreign key constraints between entities** so that **data integrity is maintained and queries are more efficient with better type safety**.

#### Acceptance Criteria - Database Schema Relationships

1. **Foreign Key Constraints**: All entity relationships have proper foreign key constraints (users.organizationId → organizations.id, etc.)
2. **Drizzle Relations**: Define Drizzle relations for all entity connections (one-to-many, many-to-many)
3. **Cascade Rules**: Implement appropriate cascade delete/update rules for dependent data
4. **Index Optimization**: Add database indexes on frequently queried foreign key columns
5. **Type Safety**: Drizzle queries return properly typed related data
6. **Migration Scripts**: Safe database migration scripts to add constraints to existing data
7. **Data Validation**: Ensure existing data integrity before applying constraints
8. **Query Optimization**: Rewrite storage layer to use relational queries instead of separate lookups
9. **Transaction Support**: Implement proper transaction handling for multi-table operations
10. **Performance Testing**: Verify query performance improvements with proper relationships

### Option B: Firebase Migration

As a **developer working on MentorMatchHub**, I want **to migrate from PostgreSQL + Drizzle to Firebase** so that **we have real-time capabilities, simplified deployment, and integrated authentication/storage**.

#### Acceptance Criteria - Firebase Migration

1. **Firestore Setup**: Configure Firestore database with proper security rules and indexes
2. **Data Migration**: Migrate all existing PostgreSQL data to Firestore collections
3. **Authentication Integration**: Replace custom auth with Firebase Authentication (Google OAuth built-in)
4. **Storage Integration**: Use Firebase Storage for file uploads (logos, documents)
5. **Real-time Updates**: Implement real-time listeners for live data updates across the app
6. **Security Rules**: Comprehensive Firestore security rules for multi-tenant data isolation
7. **Cloud Functions**: Migrate server-side logic to Firebase Cloud Functions
8. **Hosting Integration**: Deploy frontend to Firebase Hosting with custom domain support
9. **Email Integration**: Use Firebase Extensions for email functionality (SendGrid/Mailgun)
10. **Performance Optimization**: Implement proper data denormalization and query optimization for Firestore

### Option C: NextJS Framework Migration

As a **developer working on MentorMatchHub**, I want **to migrate from the current Express + React setup to NextJS** so that **we have better SEO, server-side rendering, simplified deployment, and modern full-stack capabilities**.

#### Acceptance Criteria - NextJS Migration

1. **App Router Setup**: Migrate to NextJS 14+ with App Router for modern routing and layouts
2. **Server Components**: Convert appropriate components to React Server Components for better performance
3. **API Routes**: Migrate Express API endpoints to NextJS API routes with proper middleware
4. **Authentication**: Implement NextAuth.js for authentication with Google OAuth and session management
5. **Database Integration**: Integrate database layer with NextJS API routes and server actions
6. **File Upload**: Implement file upload using NextJS API routes with proper validation and storage
7. **Email Integration**: Set up email functionality using NextJS API routes and chosen email provider
8. **SEO Optimization**: Implement proper meta tags, Open Graph, and structured data for public pages
9. **Performance**: Optimize with NextJS features (Image optimization, font optimization, bundle analysis)
10. **Deployment**: Set up Vercel deployment with environment variables and database connections
11. **Middleware**: Implement NextJS middleware for authentication, rate limiting, and request processing
12. **Static Generation**: Use static generation for public form pages and marketing content
13. **Error Handling**: Implement proper error boundaries and error pages
14. **TypeScript Integration**: Ensure full TypeScript support throughout the NextJS application
15. **Testing Setup**: Migrate and enhance testing setup for NextJS environment

---

## 3. API Contract Definition

**Note**: The primary feature (Code Organization) is structural refactoring that doesn't require new API endpoints. The secondary feature (Email System Enhancement) primarily involves backend service integration. The tertiary feature (Profile Upload Infrastructure) has its API contract defined below. The quaternary feature (Google OAuth Integration) API contract follows the profile upload specification.

```yaml
openapi: 3.0.0
info:
  title: MentorMatchHub Profile Upload & OAuth API
  description: API endpoints for file uploads and Google OAuth 2.0 authentication integration
  version: 1.0.0

paths:
  /api/organizations/{organizationId}/logo:
    post:
      summary: Upload organization logo
      description: Upload and process a logo image for an organization's branding
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: integer
          description: The ID of the organization
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                logo:
                  type: string
                  format: binary
                  description: The logo image file (PNG, JPG, JPEG, max 5MB)
              required:
                - logo
      responses:
        '201':
          description: Logo uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  logoUrl:
                    type: string
                    example: 'https://storage.example.com/logos/org-123-logo-abc123.jpg'
                  message:
                    type: string
                    example: 'Logo uploaded successfully'
        '400':
          description: Bad request - invalid file or parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'Invalid file type. Only PNG, JPG, and JPEG files are allowed'
                  errors:
                    type: array
                    items:
                      type: string
                    example: ['File size exceeds 5MB limit']
        '401':
          description: Unauthorized - user not authenticated
        '403':
          description: Forbidden - user not authorized for this organization
        '404':
          description: Organization not found
        '413':
          description: Payload too large
        '500':
          description: Internal server error
      security:
        - SessionAuth: []

    delete:
      summary: Remove organization logo
      description: Remove the current logo for an organization
      parameters:
        - name: organizationId
          in: path
          required: true
          schema:
            type: integer
          description: The ID of the organization
      responses:
        '200':
          description: Logo removed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 'Logo removed successfully'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Organization not found
        '500':
          description: Internal server error
      security:
        - SessionAuth: []

  /api/auth/google:
    get:
      summary: Initiate Google OAuth flow
      description: Redirects user to Google OAuth consent screen to begin authentication
      responses:
        '302':
          description: Redirect to Google OAuth consent screen
          headers:
            Location:
              schema:
                type: string
                example: 'https://accounts.google.com/oauth/authorize?client_id=...'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'OAuth configuration error'

  /api/auth/google/callback:
    get:
      summary: Handle Google OAuth callback
      description: Processes the OAuth callback from Google and creates/authenticates user
      parameters:
        - name: code
          in: query
          required: true
          schema:
            type: string
          description: Authorization code from Google
        - name: state
          in: query
          required: false
          schema:
            type: string
          description: State parameter for CSRF protection
      responses:
        '302':
          description: Successful authentication - redirect to dashboard
          headers:
            Location:
              schema:
                type: string
                example: '/'
            Set-Cookie:
              schema:
                type: string
                example: 'session=abc123; HttpOnly; Secure'
        '200':
          description: Authentication successful - return user data
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        example: 'google_123456789'
                      email:
                        type: string
                        example: '<EMAIL>'
                      firstName:
                        type: string
                        example: 'John'
                      lastName:
                        type: string
                        example: 'Doe'
                      profileImageUrl:
                        type: string
                        example: 'https://lh3.googleusercontent.com/...'
                      role:
                        type: string
                        example: 'admin'
                  message:
                    type: string
                    example: 'Authentication successful'
        '400':
          description: Bad request - invalid OAuth parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'Invalid authorization code or state parameter'
                  error:
                    type: string
                    example: 'invalid_grant'
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'Google authentication failed'
        '403':
          description: Access denied by user
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'User denied access to Google account'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'OAuth service temporarily unavailable'

  /api/auth/logout:
    post:
      summary: Logout user
      description: Invalidates the current user session and clears authentication cookies
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 'Logout successful'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 'Failed to logout'

components:
  securitySchemes:
    SessionAuth:
      type: apiKey
      in: cookie
      name: session
      description: Session-based authentication using secure HTTP-only cookies
```
